<!DOCTYPE html>
<html lang="ch">
	<head>
		<title>设置分区</title>
		<!-- 设置 viewport -->
		<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
		<!-- IE -->
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<!-- 兼容国产浏览器的高速模式 -->
		<meta name="renderer" content="webkit">
		<meta name="Author" content="<EMAIL>" />
		<meta name="Keywords" content="" />
		<meta name="Description" content="" />
		<meta charset="UTF-8">
		<link rel="stylesheet" type="text/css" href="css/reset.css">
		<link rel="stylesheet" href="css/translateCommon.css">
		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="css/modalbox.css" />
		<link rel="stylesheet" href="css/stepPlug.css" />
		<link rel="stylesheet" href="plugin/spectrum/spectrum.css">
		<link rel="stylesheet" href="css/seat3.css" />
		<link rel="stylesheet" href="css/step3only.css" />

	</head>
	<body>
		<div id="section">
			<!-- header -->
			<div id="header">
				<div class="hui">
					<img src="images/hui.jpg" alt="">
				</div>
				<div class="logo">
					<h1 class="f_left">
						<a href="#">
							<img src="images/logo.png" alt=""></a>
					</h1>
					<div class="f_right logo_r">
						<ul class="logo_r_show">
							<!-- <li class="search">
								<p>
									<span class="f_left">受邀者</span>
									<input class="f_left" type="text"> <i></i>
								</p>
							</li> -->
							<li class="translate-box">
								<a class="translate-box-cn" href="javascript:translate.changeLanguage('english');javascript:void(0);">
									<img src="images/translateEn.png" alt="">
								</a>
								<a class="translate-box-en" style="display: none;"
									href="javascript:translate.changeLanguage('chinese_simplified');javascript:void(0);">
									<img src="images/translateCn.png" alt="">
								</a>
							</li>
							<li class="h_qiandao">
								<a href="#">微信签到</a>
							</li>
							<li class="prompt">
								<a href="#">
									<span>0</span>
								</a>
							</li>
							<li class="help">帮助与文档</li>
							<li class="tel">18621589099</li>
						</ul>
					</div>
				</div>
			</div>

			<!-- content -->
			<div id="content" class="clearfix">
				<!-- 左边导航 -->
				<div class="c_left">
					<ul class="c_left_nav f_left">
						<li class="current" title="活动">
							<img src="images/icon6.png" alt="">
						</li>
						<li title="应用">
							<img src="images/icon7.png" alt="">
						</li>
						<li title="联系人">
							<img src="images/icon8.png" alt="">
						</li>
						<li title="供应商">
							<img src="images/icon9.png" alt="">
						</li>
						<li title="账户">
							<img src="images/icon10.png" alt="">
						</li>
					</ul>

				</div>

				<!-- 主要模块 -->
				<div class="c_right">
					<div class="main">
						<div class="new-content-top">
							<span class="layui-breadcrumb" lay-separator=">">
								<a href="">首页</a>
								<a href="">活动列表</a>
								<a href="">xoxcxxxx活动</a>
								<a><cite>活动总览</cite></a>
							</span>
							<div class="content-top-btn">
								<button class="layui-btn" type="button" onclick="proveStep()">上一步</button>
								<button class="layui-btn" type="button" onclick="">保存</button>
								<button class="layui-btn" type="button" onclick="">进入场地视图设计</button>
								<button class="layui-btn layui-btn-disabled" type="button" onclick="">进入座位分配</button>
							</div>
						</div>
						<div class="main-content">
							<div class="main-left">
								<!-- 放大缩小 -->
								<div class="zoomBox">
									<i class="layui-icon layui-icon-reduce-circle" onclick="zoomFn('in')"></i>
									<i class="layui-icon layui-icon-add-circle" onclick="zoomFn('out')"></i>
								</div>

								<!-- 步骤条 -->
								<div class="step-html" style="width: 600px;">
									<div class="step-box">
										<div class="step-list step-act">
											<span class="step-number">1</span>
										</div>
										<div class="step-list-html-text step-act">设置场地排列</div>
										<div class="step-line"></div>
										<div class="step-list step-act">
											<span class="step-number">2</span>
										</div>
										<div class="step-list-html-text step-act">设置场地布局</div>
										<div class="step-line"></div>
										<div class="step-list step-act">
											<span class="step-number">3</span>
										</div>
										<div class="step-list-html-text step-act">设置分区</div>
										<!-- <div class="step-line"></div>
										<div class="step-list">
											<span class="step-number">4</span>
										</div>
										<div class="step-list-html-text">设置效果图</div> -->
									</div>
								</div>
								<div class="seat-setting-tips" style="color: transparent;">*这是一段占位符</div>
								<!-- 座位遮罩层 -->
								<!-- <div class="seatFixed" draggable="true" onclick="showClickMessage()" ondrag="showDragMessage()"></div> -->
								<div class="seatBoxContent" id="seatBoxContent">

									<div class="newSeatBox">
										<div class="seatBox" id="seatBox" style="width: auto;transform: scale(1);" zoom="1">

										</div>
									</div>

								</div>
							</div>
							<div class="main-right">
								<div class="container">

									<div class="layui-tab" lay-filter="tab-hash">
										<ul class="layui-tab-title">
											<li class="layui-this" lay-id="11">分区列表</li>
											<!-- <li lay-id="22">分配座位</li> -->
											<!-- <li lay-id="33">分配座位</li> -->
										</ul>
										<div class="layui-tab-content">
											<div class="layui-tab-item layui-show">
												<h4 class="text-danger">*根据颜色进行分区</h4>
												<h4 class="text-danger">先选中要设置分区的座位，然后填写名车，选择颜色后点击“保存分区”按钮，这个动作可以重复操作N遍，全部设置完了之后点击“分配座位”</h4>

												<div class="groupBox">
													<ul class="groupUlBox">
														<!-- <li>
															<span class="colorSpan"></span>
															<div class="groupText">A区：100</div>
															<div class="groupText2">已分配：100</div>
															<div class="groupAllocation">分配座位</div>
														</li> -->
													</ul>

													<div class="seatDatas" id="seatDatas">
														座位总数：<span></span>
													</div>
												</div>

												<div class="addItem" style="display: none;">
													<div class="content-input">
														<div class="content-input-title">分区名称：</div>
														<div class="content-input-box">
															<input type="text" class="layui-input" id="groupName" placeholder="名称" value="a1">
														</div>
													</div>
													<div class="content-input">
														<div class="content-input-title">选中数量：</div>
														<div class="content-input-box">
															<span id="groupNumber">0</span>
														</div>
													</div>
													<div class="content-input">
														<div class="content-input-title">分区颜色：</div>
														<div class="content-input-box">
															<input type="text" class="form-control" id="color" placeholder="座位颜色">
														</div>
													</div>
												</div>


											</div>

											<div class="bottom-btn" style="margin-top: 90px;">
												<button class="layui-btn layui-bg-blue addNewGroup" type="button"
													onclick="addNewGroup()">新建分区</button>
												<button class="layui-btn" type="button" id="set-price" style="display: none;">保存分区</button>
												<button class="layui-btn layui-bg-blue" type="button" onclick="assignSeats()" id="goAssign"
													style="display: none;">保存场地图</button>
											</div>
											<!-- end -->
										</div>
									</div>


								</div>
							</div>
						</div>


					</div>
				</div>
			</div>
		</div>
		<!-- 灰色背景 -->
		<div id="f-fade" class="black_overlay"></div>
		<!-- 加载弹窗 -->
		<div class="loading-module">
			<div class="loading-module-bg">
				<div class="loadingSix">
					<span></span>
					<span></span>
					<span></span>
					<span></span>
					<span></span>
					<span></span>
					<span></span>
					<span></span>
				</div>
				<div class="loading-text-box">页面正在加载中，请稍等。。。。</div>
			</div>
		</div>

		<!-- 修改 -->
		<div class="white-contentAll" id="light1" style="display:none;width: 500px;">
			<div class="white-header">
				<span class="white-headerTitle">
					修改
				</span>
				<span class="white-delete" onclick="cancel('light1')">
				</span>
			</div>
			<div class="white-body">
				<div class="edit-module-box">
					<div class="edit-module-flex">
						<span>区域名称：</span>
						<input type="text" class="layui-input editName" />
					</div>
					<div class="edit-module-flex">
						<span>区域颜色：</span>
						<input type="text" id="editColor" placeholder="座位颜色">
					</div>
				</div>
			</div>
			<div class="white-footer">
				<button class="white-close" onclick="cancel('light1')">
					取消
				</button>
				<button class="white-sure white-sureAdd" onclick="editSure()">
					确定
				</button>
			</div>
		</div>

		<!-- 删除 -->
		<div class="white-contentAll" id="light2" style="display:none;width: 500px;">
			<div class="white-header">
				<span class="white-headerTitle">
					提示
				</span>
				<span class="white-delete" onclick="cancel('light2')">
				</span>
			</div>
			<div class="white-body">
				<div class="delete-module-text">您确定要删除吗？删除后会释放当前区域所有座位数量</div>
			</div>
			<div class="white-footer">
				<!-- <button class="white-close" onclick="cancel('light1')">
					取消
				</button> -->
				<button class="white-sure white-sureAdd" onclick="cancel('light2')">
					确定
				</button>
			</div>
		</div>

		<!--  -->
		<div class="white-contentAll" id="light20241118" style="display:none;width: 500px;">
			<div class="white-header">
				<span class="white-headerTitle">
					提示
				</span>
				<span class="white-delete" onclick="cancel('light20241118')">
				</span>
			</div>
			<div class="white-body">
				<div class="delete-module-text">所有分区自动删除，确定返回上一步吗?</div>
			</div>
			<div class="white-footer">
				<button class="white-sure white-sureAdd" onclick="cancel('light20241118')">
					确定
				</button>
			</div>
		</div>



		<!-- <script src="js/jquery-1.7.2.min.js"></script> -->
		<script src="js/jquery-3.6.3.min.js"></script>
		<script src="js/index.js"></script>
		<script>
			// 遮罩层
			function viewTask(id) {
				$("div[id^=light]").hide();
				$("#" + id).show();
				$("#f-fade").show();
			}

			function cancel(id) {
				$("#" + id).hide();
				$("#f-fade").hide();
			}
		</script>

		<!-- <script src="translateJs/translate.js"></script>
		<script src="translateJs/translateCommon.js"></script> -->

		<script src="plugin/spectrum/spectrum.js"></script>
		<script src="plugin/jdialog/JDialog.min.js"></script>
		<script src="layui/layui.js"></script>
		<script>
			var layer = "";
			layui.use(function() {
				var element = layui.element;
				layer = layui.layer;
				element.on('tab(tab-hash)', function(obj) {
					let thisIndex = obj.index
					if (thisIndex == 1) {
						assignSeats()

					}
				});
			});
		</script>
		<script src="js/seat.js"></script>
		<script>
			let seatRows = localStorage.getItem("seatRows");
			let seatCols = localStorage.getItem("seatCols");
			console.log(seatCols)
			try {
				var datas = JSON.parse(localStorage.getItem("seats"));
				console.log(datas)
			} catch (e) {}
			var seats = $.seats({
				box: "#seatBox",
				step: 2,
				rows: seatRows,
				cols: seatCols,
				size: 20, //座位的尺寸，单位：像素	
				datas: datas
			});

			console.log(datas)
			// var groupObject = []
			$('#set-price').on('click', function() {
				// localStorage.clear();
				// 获取已经保存的组别
				var groupObject = JSON.parse(localStorage.getItem('groupArrayObject')) || []
				console.log(groupObject)
				// console.log(groupObject.length)

				var color = $('#color').val();
				console.log(color)
				if (color == "hsv(0, 100%, 100%)") {
					// console.log(111111111)
					color = "rgb(255, 0, 0)"
				}
				var price = $('#price').val();
				var groupName = $('#groupName').val(); // 获取分组名称
				if (groupName == '') {
					// alert("分组名字不能为空")
					layer.msg('分组名字不能为空', {
						icon: 2,
						time: 1000 // 设置 2 秒后自动关闭
					});
					return
				}
				seats.setPrice(price, color, groupName);
				//更新座位信息
				localStorage.setItem("seats", JSON.stringify(seats.getSeats()));

				console.log(seats.getSeats())
				let groupNumber = $("#groupNumber").text()
				let newGroup = {
					groupName,
					color,
					groupNumber
				}
				groupObject.push(newGroup)
				// 保存分组情况
				localStorage.setItem("groupArrayObject", JSON.stringify(groupObject));

				layer.msg('更新分区成功', {
					icon: 1,
					time: 2000 // 设置 2 秒后自动关闭
				});
				console.log(groupObject)

				// $("#groupName").val('')
				$("#groupNumber").text(0)
				setTimeout(function() {
					location.reload();
				}, 500);
			});

			$("#color").spectrum({
				showPalette: true, // 显示选择器面板
				// showPaletteOnly: true, //只显示选择器面板
				showInput: true,
				preferredFormat: "hex3",
				color: 'rgb(255, 0, 0)',
				palette: [
					["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
						"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
					],
				],
				change: function(color) {
					$("#color").val(color.toHexString());
				}
			});
			var editColor = "rgb(255, 153, 0)"
			// 编辑颜色
			$("#editColor").spectrum({
				showPalette: true, // 显示选择器面板
				// showPaletteOnly: true, //只显示选择器面板
				showInput: true,
				preferredFormat: "hex3",
				color: editColor,
				palette: [
					["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
						"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
					],
				],
				change: function(color) {
					$("#editColor").val(color.toHexString());
				}
			});


			// function nextStep() {
			// 	window.location.href = "step4.html"
			// }
			// 分配座位
			function assignSeats() {
				window.location.href = "step4.html"
			}

			// 获取分区列表
			function getGroupItem() {
				// 统计座位总数
				$("#seatDatas").find("span").text(Object.keys(datas).length)

				// 获取已经保存的组别
				var liHtml = ""
				var groupObject = JSON.parse(localStorage.getItem('groupArrayObject')) || []
				var noGroupNumber = 0
				if (groupObject.length > 0) {

					var nowGroupObject = []

					for (let item of groupObject) {
						console.log(item)
						let nowGroupNumber = $(`span[name='${item.groupName}']`).length
						noGroupNumber += Number(nowGroupNumber)
						if (nowGroupNumber > 0) {
							nowGroupObject.push({
								color: item.color,
								groupName: item.groupName,
								groupNumber: nowGroupNumber
							})
							liHtml += `<li>
														<span class="colorSpan" style="background:${item.color}"></span>
														<div class="groupText">${item.groupName}区：${nowGroupNumber}</div>
														<button type="button" class="layui-btn layui-btn-sm" onclick="editGroupName(this,'${item.groupName}')">修改</button>
														<button type="button" class="layui-btn layui-btn-sm layui-bg-red" onclick="deleteGroup(this,'${item.groupName}')">删除</button>
													</li>`
						}

					}
					noGroupNumber = Object.keys(datas).length - noGroupNumber
					// if (noGroupNumber <= 0) {
					// 	noGroupNumber == 0
					// }
					liHtml += `<li>
													<span class="colorSpan" style="background:#B9DEA0;"></span>
													<div class="groupText">未分区：${noGroupNumber}</div>
												</li>`
					$(".groupUlBox").html(liHtml)

					// 重新保存数组
					console.log(nowGroupObject)
					localStorage.setItem("groupArrayObject", JSON.stringify(nowGroupObject));

					// 如果未分区为0，这出现分配座位按钮，新建分区取消
					if (noGroupNumber <= 0) {
						$("#goAssign").show();
						$(".addNewGroup").hide()
					}

				}
			}
			$(function() {
				getGroupItem()
			})
			// 新建分区
			function addNewGroup() {

				var groupObject = JSON.parse(localStorage.getItem('groupArrayObject')) || []
				console.log(groupObject.length)
				$("#groupName").val(`a${groupObject.length+1}`)
				$(".addItem").show()
				$(".addNewGroup,.seatFixed").hide()
				$("#set-price").show();
				$("#goAssign").hide()
				$("#color").spectrum("show")
			}
		</script>
		<script>
			function showClickMessage() {
				console.log("Click event occurred")
				addNewGroup()
			}

			function showDragMessage() {
				console.log("Drag event occurred")
				addNewGroup()
			}

			// 编辑分组名称
			var initialName = ''

			function editGroupName(obj, groupName) {

				// 获取当前分组的颜色
				let groupColor = $(obj).parent().find(".colorSpan").css("background-color")
				// 赋值给弹窗里的颜色控件
				editColor = groupColor
				console.log(groupColor)

				$("#editColor").spectrum({
					showPalette: true, // 显示选择器面板
					// showPaletteOnly: true, //只显示选择器面板
					showInput: true,
					preferredFormat: "hex3",
					color: editColor,
					palette: [
						["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
							"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
						],
					],
					change: function(color) {
						$("#editColor").val(color.toHexString());
					}
				});



				console.log(groupName)
				initialName = groupName
				// let name = groupName;
				$("#f-fade,#light1").show()
				$(".editName").val(groupName)


			}

			// 修改确定
			function editSure() {
				let groupName = $(".editName").val()
				let groupColor = $("#editColor").val()
				// console.log(groupName, groupColor)
				// console.log(initialName)
				// 先修改颜色，再修改名称
				$(`span[name='${initialName}']`).css({
					"background": groupColor
				})
				$(`span[name='${initialName}']`).attr("name", groupName)
				$("#f-fade,#light1").hide()

			}

			//删除
			function deleteGroup(obj, groupName) {
				viewTask('light2')
			}

			function getScaleplate() {
				$(".newSeatBox").width($(".seatBox").width() + 20)
				$(".tdBox,.trBox").remove()
				let tdLength = $(".seatBox").find("table").find("tr").eq(0).find("td").length
				let trLength = $(".seatBox").find("table").find("tr").length
				console.log(trLength)
				let tdSpan = ""
				for (let i = 1; i <= tdLength; i++) {
					tdSpan += `<span>${i}</span>`
				}
				$(".seatBox").before(`<div class="tdBox">
															${tdSpan}
															</div>`)
				let trSpan = ""
				for (let i = 1; i <= trLength; i++) {
					trSpan += `<span>${i}</span>`
				}
				$(".seatBox").find("table").before(`<div class="trBox">
															${trSpan}
															</div>`)
			}
			getScaleplate()
		</script>

		<script>
			// 放大缩小
			function zoomFn(obj) {
				let zoomVal = Number((Number($("#seatBox").attr("zoom"))).toFixed(1))
				if (obj == "in") {
					if (zoomVal >= 0.2) {
						zoomVal = zoomVal - 0.1
						$("#seatBox").css({
							transform: "scale(" + zoomVal + ")"
						})
					}

				} else if (obj == "out") {

					if (zoomVal < 1) {
						zoomVal = zoomVal + 0.1
						$("#seatBox").css({
							transform: "scale(" + zoomVal + ")"
						})
					}

				}
				$("#seatBox").attr("zoom", zoomVal)
			}

			// 上一步
			function proveStep() {
				localStorage.setItem("seats", JSON.stringify(seats.getSeats()));
				location.href = "step2.html";
			}
		</script>


	</body>
</html>